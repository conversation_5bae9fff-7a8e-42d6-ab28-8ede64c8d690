﻿/********************************************************************
 * 文件名： graphicsitemgroup.cpp
 * 时间：   2024-05-26 19:44:24
 * 开发者：  mhf
 * 邮箱：   <EMAIL>
 * 说明：   瓦片图元组
 * ******************************************************************/
#include "graphicsitemgroup.h"
#include "bingformula.h"

#include <qfont.h>
#include <qgraphicsscene.h>
#include <QDebug>
#include <QPen>

GraphicsItemGroup::GraphicsItemGroup(QGraphicsItem* parent)
    : QGraphicsItemGroup(parent)
{}

GraphicsItemGroup::~GraphicsItemGroup() {}

/**
 * @brief        添加绘制瓦片图
 * @param info
 */
void GraphicsItemGroup::addImage(const ImageInfo& info)
{
    quint64 key = (info.x << 24) + info.y;
    if (m_itemsImg.contains(key))   // 如果瓦片已经存在则直接绘制
    {
        m_itemsImg[key]->setPixmap(info.img);
        m_itemsImg[key]->show();
    }
    else   // 如果瓦片不存在则添加图元
    {
        // 绘制瓦片图
        auto* item = new QGraphicsPixmapItem(info.img);
        QPoint pos = Bing::tileXYToPixelXY(QPoint(info.x, info.y));
        item->setPos(pos);
        this->addToGroup(item);
        m_itemsImg[key] = item;
    }
}
