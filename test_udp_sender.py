#!/usr/bin/env python3
"""
UDP组播测试发送器
用于测试Qt地图应用程序的目标管理功能
"""

import socket
import json
import time
import random

def send_target_data():
    # 创建UDP套接字
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    # 组播地址和端口
    multicast_group = '234.5.6.7'
    port = 5000
    
    # 测试目标数据
    targets = [
        {
            "targetID": "RED_001",
            "targetType": "red",
            "LON": 120.1234,
            "LAT": 30.2345
        },
        {
            "targetID": "BLUE_001", 
            "targetType": "blue",
            "LON": 120.5678,
            "LAT": 30.6789
        },
        {
            "targetID": "RED_002",
            "targetType": "red", 
            "LON": 121.1111,
            "LAT": 31.2222
        }
    ]
    
    print(f"开始发送测试数据到 {multicast_group}:{port}")
    
    try:
        for i in range(10):  # 发送10轮数据
            for target in targets:
                # 随机微调位置模拟移动
                target["LON"] += random.uniform(-0.001, 0.001)
                target["LAT"] += random.uniform(-0.001, 0.001)
                
                # 转换为JSON
                message = json.dumps(target)
                
                # 发送数据
                sock.sendto(message.encode('utf-8'), (multicast_group, port))
                print(f"发送: {message}")
                
                time.sleep(1)  # 每秒发送一个目标
            
            print(f"--- 第 {i+1} 轮发送完成 ---")
            time.sleep(2)  # 每轮间隔2秒
            
    except KeyboardInterrupt:
        print("\n发送中断")
    finally:
        sock.close()
        print("UDP发送器已关闭")

if __name__ == "__main__":
    send_target_data()
