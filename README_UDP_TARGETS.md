# Qt地图应用程序 - UDP目标管理功能

## 功能概述

本应用程序为Qt地图应用添加了完整的UDP组播目标管理系统，包括以下主要功能：

### 1. UDP模块和配置界面
- 点击"udp配置"按钮打开配置对话框
- 支持设置组播地址（默认：*********）
- 支持设置端口号（默认：5000）
- 支持选择网络接口
- 提供连接/断开功能

### 2. 目标管理系统
- 监听UDP组播接收JSON消息
- 根据targetType自动加载对应图标：
  - "red" → red_ship.png
  - "blue" → blue_ship.png
- 实时更新目标位置
- 智能处理地图缩放时的目标位置更新

### 3. 鼠标位置显示
- 实时显示鼠标所在位置的经纬度
- 格式：经度: xxx.xxxxxx, 纬度: yy.yyyyyy

### 4. 目标点击交互
- 点击目标图标发送UDP消息
- 显示目标详细信息框
- 点击其他区域自动隐藏信息框

### 5. 鼠标右击导航
- 右击地图任意位置将视窗中心设置为北京
- 自动计算并移动到北京坐标（经度: 116.3974, 纬度: 39.9042）
- 平滑滚动到目标位置并更新瓦片显示

## JSON消息格式

### 接收的目标数据格式：
```json
{
    "targetID": "目标唯一标识",
    "targetType": "red|blue", 
    "LON": 经度数值,
    "LAT": 纬度数值
}
```

### 发送的点击消息格式：
```json
{
    "targetID": "被点击目标的ID"
}
```

## 核心技术特性

### 智能瓦片加载等待机制
当用户缩放地图层级时，系统会：
1. 标记层级正在改变
2. 等待新层级的瓦片加载完成
3. 使用定时器确保所有瓦片都已加载
4. 然后重新计算并更新所有目标位置

这确保了目标图标始终正确显示在新的瓦片层级上。

### 关键实现细节：
- `m_levelChanging`: 标记层级变化状态
- `m_pendingTargetUpdate`: 标记是否有待更新的目标
- `m_tileCheckTimer`: 200ms延迟定时器，确保瓦片加载完成
- `onTileLoaded()`: 监听瓦片加载事件
- `checkTileLoadingComplete()`: 检查并执行目标位置更新

## 使用方法

### 1. 启动应用程序
编译并运行Qt应用程序

### 2. 配置UDP连接
- 点击"udp配置"按钮
- 设置组播地址和端口（或使用默认值）
- 选择网络接口（可选）
- 点击"连接"

### 3. 测试目标显示
使用提供的Python测试脚本：
```bash
python test_udp_sender.py
```

### 4. 交互操作
- 移动鼠标查看经纬度显示
- 缩放地图观察目标位置自动更新
- 左击目标图标查看详细信息
- 右击地图任意位置快速导航到北京
- 观察控制台日志了解系统运行状态

## 测试数据示例

```json
{"targetID": "RED_001", "targetType": "red", "LON": 120.1234, "LAT": 30.2345}
{"targetID": "BLUE_001", "targetType": "blue", "LON": 120.5678, "LAT": 30.6789}
{"targetID": "RED_002", "targetType": "red", "LON": 121.1111, "LAT": 31.2222}
```

## 调试信息

应用程序会在控制台输出详细的调试信息：
- UDP连接状态
- 目标创建和更新
- 层级变化和瓦片加载
- 位置计算和更新过程
- 右击导航到北京的操作

## 注意事项

1. 确保防火墙允许UDP组播通信
2. 网络接口选择会影响组播接收
3. 目标图标资源文件必须存在于targetUI.qrc中
4. 经纬度坐标使用WGS84标准
5. 层级变化时会有短暂延迟以确保瓦片加载完成
