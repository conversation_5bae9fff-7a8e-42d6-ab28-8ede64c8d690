﻿#include "widget.h"
#include "ui_widget.h"
#include "MapView/bingformula.h"
#include <QApplication>
#include <QDebug>
#include <QtMath>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMessageBox>
#include <QDialog>
#include <QFormLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QNetworkInterface>
#include <QGraphicsSceneMouseEvent>
#include <QPainter>
#include <QBrush>
#include <QPen>
#include <QTimer>
#include <limits>

// 自定义角色常量
static const int TargetRole = Qt::UserRole + 1;
static const int TargetIdRole = Qt::UserRole + 2;

// InfoLabel实现
InfoLabel::InfoLabel(QWidget* parent) : QLabel(parent)
{
    setWindowFlags(Qt::ToolTip);
    setStyleSheet("QLabel { background-color: rgba(255, 255, 255, 230); border: 1px solid black; padding: 5px; border-radius: 3px; }");
    hide();
}

void InfoLabel::showTargetInfo(const TargetInfo& info, const QPoint& position)
{
    QString text = QString("ID: %1\n类型: %2\n经度: %3\n纬度: %4")
                       .arg(info.id)
                       .arg(info.type == "red" ? "红方" : "蓝方")
                       .arg(info.lon, 0, 'f', 6)
                       .arg(info.lat, 0, 'f', 6);
    setText(text);
    adjustSize();
    move(position.x() + 10, position.y() + 10);
    show();
    raise();
}

void InfoLabel::hideInfo()
{
    hide();
}

void InfoLabel::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制背景
    QBrush brush(QColor(255, 255, 255, 230));
    QPen pen(Qt::black, 1);
    painter.setBrush(brush);
    painter.setPen(pen);
    painter.drawRoundedRect(rect().adjusted(0, 0, -1, -1), 3, 3);

    QLabel::paintEvent(event);
}

Widget::Widget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::Widget)
{
    ui->setupUi(this);
    this->resize(1500, 1200);
    qApp->setStyleSheet("*{font: 12pt '宋体';}");
    this->setWindowTitle(QString("QT加载显示在线瓦片地图示例（绝对像素坐标）--V%1").arg(APP_VERSION));

    // 初始化信息标签
    m_infoLabel = new InfoLabel(this);

    // 初始化瓦片检查定时器
    m_tileCheckTimer = new QTimer(this);
    m_tileCheckTimer->setSingleShot(true);
    m_tileCheckTimer->setInterval(200); // 200ms延迟
    connect(m_tileCheckTimer, &QTimer::timeout, this, &Widget::checkTileLoadingComplete);

    qRegisterMetaType<ImageInfo>("ImageInfo");
    m_geturl = new GetUrl();
    m_geturl->setUrl("http://dynamic.t0.tiles.ditu.live.com/comp/ch/a{q}.jpg?it=G,OS,L&mkt=zh-cn&cstl=w4c&ur=cn");

    // 连接信号槽
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::showRect, this, &Widget::showRect);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::setLevel, this, &Widget::onLevelChanged);
    connect(GetUrlInterface::getInterface(), &GetUrlInterface::update, this, &Widget::onTileLoaded);

    // 连接UDP配置按钮
    connect(ui->pushButton, &QPushButton::clicked, this, &Widget::onUdpConfigClicked);

    // 连接鼠标移动信号
    connect(ui->graphicsView, &MapGraphicsView::mousePos, this, &Widget::onMouseMove);

    // 安装事件过滤器
    ui->graphicsView->scene()->installEventFilter(this);
}

Widget::~Widget()
{
    disconnectUdp();
    delete m_geturl;
    delete ui;
}

bool Widget::eventFilter(QObject* watched, QEvent* event)
{
    if (watched == ui->graphicsView->scene())
    {
        if (event->type() == QEvent::GraphicsSceneMousePress)
        {
            auto* mouseEvent = static_cast<QGraphicsSceneMouseEvent*>(event);
            QPointF scenePos = mouseEvent->scenePos();

            // 检查是否点击了目标
            QGraphicsItem* item = ui->graphicsView->scene()->itemAt(scenePos, QTransform());
            if (item && item->data(TargetRole).toBool())
            {
                QString targetId = item->data(TargetIdRole).toString();
                if (m_targets.contains(targetId))
                {
                    // 发送目标点击消息
                    sendTargetClicked(targetId);

                    // 显示目标信息
                    QPoint globalPos = ui->graphicsView->mapToGlobal(ui->graphicsView->mapFromScene(scenePos));
                    m_infoLabel->showTargetInfo(m_targets[targetId], globalPos);
                    m_currentSelectedTarget = targetId;

                    return true;
                }
            }
            else
            {
                // 点击了其他区域，隐藏信息框
                hideTargetInfo();
            }
        }
    }

    return QWidget::eventFilter(watched, event);
}

void Widget::onUdpConfigClicked()
{
    openUdpConfigDialog();
}

void Widget::onReadyRead()
{
    while (m_udpSocket && m_udpSocket->hasPendingDatagrams())
    {
        QByteArray datagram;
        datagram.resize(int(m_udpSocket->pendingDatagramSize()));
        QHostAddress sender;
        quint16 senderPort;

        m_udpSocket->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);
        processUdpMessage(datagram);
    }
}

void Widget::onMouseMove(QPoint scenePos)
{
    // 将场景坐标转换为经纬度
    qreal lon, lat;
    Bing::pixelXYToLatLong(scenePos, m_currentLevel, lon, lat);

    // 更新底部标签显示
    ui->lonlat->setText(QString("经度: %1, 纬度: %2").arg(lon, 0, 'f', 6).arg(lat, 0, 'f', 6));
}

void Widget::onLevelChanged(int level)
{
    m_currentLevel = level;
    ui->line_level->setText(QString("层级：%1").arg(level));

    // 标记层级正在改变，等待瓦片加载完成
    m_levelChanging = true;
    m_pendingTargetUpdate = !m_targets.isEmpty();

    qDebug() << "层级改变到:" << level << "，等待瓦片加载完成后更新目标位置";
}

void Widget::onTileLoaded(const ImageInfo& info)
{
    // 检查是否是当前层级的瓦片
    if (info.z == m_currentLevel && m_levelChanging && m_pendingTargetUpdate)
    {
        qDebug() << "当前层级瓦片加载:" << info.x << "," << info.y << "层级:" << info.z;

        // 重启定时器，等待所有瓦片加载完成
        m_tileCheckTimer->start();
    }
}

/**
 * @brief        当前显示的地图像素范围
 * @param rect
 */
void Widget::showRect(QRect rect)
{
    QString str = QString("[%1，%2]-[%3，%4]").arg(rect.x()).arg(rect.y()).arg(rect.right()).arg(rect.bottom());
    Q_UNUSED(str);
    //ui->line_mapShowRect->setText(str);
}

void Widget::openUdpConfigDialog()
{
    QDialog dialog(this);
    dialog.setWindowTitle("UDP配置");
    dialog.setModal(true);
    dialog.resize(350, 200);

    // 创建控件
    QLineEdit* groupEdit = new QLineEdit(&dialog);
    groupEdit->setText(m_groupAddress.isNull() ? "*********" : m_groupAddress.toString());
    groupEdit->setPlaceholderText("组播地址");

    QLineEdit* portEdit = new QLineEdit(&dialog);
    portEdit->setText(m_groupPort == 0 ? "5000" : QString::number(m_groupPort));
    portEdit->setPlaceholderText("端口号");

    QComboBox* interfaceCombo = new QComboBox(&dialog);
    interfaceCombo->addItem("默认接口", QString());

    // 添加网络接口
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    for (const QNetworkInterface& iface : interfaces)
    {
        if (iface.flags() & QNetworkInterface::IsUp &&
            iface.flags() & QNetworkInterface::IsRunning &&
            !(iface.flags() & QNetworkInterface::IsLoopBack))
        {
            interfaceCombo->addItem(QString("%1 (%2)").arg(iface.humanReadableName(), iface.name()), iface.name());
        }
    }

    // 设置当前选择的接口
    int currentIndex = interfaceCombo->findData(m_interfaceName);
    if (currentIndex >= 0)
        interfaceCombo->setCurrentIndex(currentIndex);

    QPushButton* connectBtn = new QPushButton("连接", &dialog);
    QPushButton* disconnectBtn = new QPushButton("断开", &dialog);
    QPushButton* cancelBtn = new QPushButton("取消", &dialog);

    // 布局
    QFormLayout* formLayout = new QFormLayout();
    formLayout->addRow("组播地址:", groupEdit);
    formLayout->addRow("端口号:", portEdit);
    formLayout->addRow("网络接口:", interfaceCombo);

    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(connectBtn);
    buttonLayout->addWidget(disconnectBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(cancelBtn);

    QVBoxLayout* mainLayout = new QVBoxLayout(&dialog);
    mainLayout->addLayout(formLayout);
    mainLayout->addLayout(buttonLayout);

    // 连接信号
    connect(connectBtn, &QPushButton::clicked, [&]() {
        QString group = groupEdit->text().trimmed();
        quint16 port = portEdit->text().trimmed().toUShort();
        QString ifaceName = interfaceCombo->currentData().toString();

        if (group.isEmpty() || port == 0)
        {
            QMessageBox::warning(&dialog, "错误", "请输入有效的组播地址和端口号");
            return;
        }

        if (connectUdp(group, port, ifaceName))
        {
            QMessageBox::information(&dialog, "成功", "UDP连接成功");
            dialog.accept();
        }
        else
        {
            QMessageBox::warning(&dialog, "错误", "UDP连接失败");
        }
    });

    connect(disconnectBtn, &QPushButton::clicked, [&]() {
        disconnectUdp();
        QMessageBox::information(&dialog, "成功", "UDP已断开");
        dialog.accept();
    });

    connect(cancelBtn, &QPushButton::clicked, &dialog, &QDialog::reject);

    dialog.exec();
}

bool Widget::connectUdp(const QString& group, quint16 port, const QString& ifaceName)
{
    // 先断开现有连接
    disconnectUdp();

    m_groupAddress = QHostAddress(group);
    m_groupPort = port;
    m_interfaceName = ifaceName;

    // 创建UDP套接字
    m_udpSocket = new QUdpSocket(this);

    // 绑定到指定端口
    if (!m_udpSocket->bind(QHostAddress::AnyIPv4, m_groupPort, QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint))
    {
        qWarning() << "UDP绑定失败:" << m_udpSocket->errorString();
        delete m_udpSocket;
        m_udpSocket = nullptr;
        return false;
    }

    // 加入组播组
    QNetworkInterface iface;
    if (!m_interfaceName.isEmpty())
    {
        iface = QNetworkInterface::interfaceFromName(m_interfaceName);
    }

    bool joinResult = false;
    if (iface.isValid())
    {
        joinResult = m_udpSocket->joinMulticastGroup(m_groupAddress, iface);
    }
    else
    {
        joinResult = m_udpSocket->joinMulticastGroup(m_groupAddress);
    }

    if (!joinResult)
    {
        qWarning() << "加入组播组失败:" << m_udpSocket->errorString();
        delete m_udpSocket;
        m_udpSocket = nullptr;
        return false;
    }

    // 连接信号
    connect(m_udpSocket, &QUdpSocket::readyRead, this, &Widget::onReadyRead);

    m_udpConnected = true;
    qDebug() << "UDP连接成功，组播地址:" << group << "端口:" << port;

    return true;
}

void Widget::disconnectUdp()
{
    if (m_udpSocket)
    {
        if (m_udpConnected && !m_groupAddress.isNull())
        {
            QNetworkInterface iface;
            if (!m_interfaceName.isEmpty())
            {
                iface = QNetworkInterface::interfaceFromName(m_interfaceName);
            }

            if (iface.isValid())
            {
                m_udpSocket->leaveMulticastGroup(m_groupAddress, iface);
            }
            else
            {
                m_udpSocket->leaveMulticastGroup(m_groupAddress);
            }
        }

        m_udpSocket->close();
        m_udpSocket->deleteLater();
        m_udpSocket = nullptr;
    }

    m_udpConnected = false;
    m_groupAddress = QHostAddress();
    m_groupPort = 0;
    m_interfaceName.clear();

    qDebug() << "UDP已断开";
}

void Widget::processUdpMessage(const QByteArray& data)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError)
    {
        qWarning() << "JSON解析错误:" << error.errorString();
        return;
    }

    if (!doc.isObject())
    {
        qWarning() << "JSON不是对象格式";
        return;
    }

    QJsonObject obj = doc.object();

    // 提取字段
    QString targetId = obj.value("targetID").toString();
    QString targetType = obj.value("targetType").toString().toLower();
    double lon = obj.value("LON").toDouble();
    double lat = obj.value("LAT").toDouble();

    // 验证数据
    if (targetId.isEmpty())
    {
        qWarning() << "目标ID为空";
        return;
    }

    if (targetType != "red" && targetType != "blue")
    {
        qWarning() << "无效的目标类型:" << targetType;
        return;
    }

    if (!qIsFinite(lon) || !qIsFinite(lat))
    {
        qWarning() << "无效的经纬度:" << lon << lat;
        return;
    }

    // 更新或创建目标
    upsertTarget(targetId, targetType, lon, lat);

    qDebug() << "处理目标:" << targetId << targetType << lon << lat;
}

void Widget::upsertTarget(const QString& id, const QString& type, double lon, double lat)
{
    bool isNewTarget = !m_targets.contains(id);

    // 如果不是新目标，先清除原有目标
    if (!isNewTarget)
    {
        TargetInfo oldInfo = m_targets[id];
        if (oldInfo.item)
        {
            ui->graphicsView->scene()->removeItem(oldInfo.item);
            delete oldInfo.item;
        }
        m_targets.remove(id);
        qDebug() << "移除旧目标:" << id;
    }

    // 创建新目标信息
    TargetInfo info;
    info.id = id;
    info.type = type;
    info.lon = lon;
    info.lat = lat;

    // 创建新的图标
    QString iconPath = (type == "red") ? ":/red_ship.png" : ":/blue_ship.png";
    QPixmap pixmap(iconPath);

    if (pixmap.isNull())
    {
        qWarning() << "无法加载图标:" << iconPath;
        return;
    }

    info.item = new QGraphicsPixmapItem(pixmap);
    info.item->setData(TargetRole, true);
    info.item->setData(TargetIdRole, id);

    // 设置图标中心对齐
    info.item->setOffset(-pixmap.width() / 2.0, -pixmap.height() / 2.0);

    // 更新位置
    QPoint pixelPos = Bing::latLongToPixelXY(lon, lat, m_currentLevel);
    info.item->setPos(pixelPos);

    // 添加到场景
    ui->graphicsView->scene()->addItem(info.item);

    // 保存到哈希表
    m_targets[id] = info;

    if (isNewTarget)
    {
        qDebug() << "创建新目标:" << id << type;
    }
    else
    {
        qDebug() << "更新目标:" << id << type;
    }

    qDebug() << "目标位置更新:" << id << "(" << lon << "," << lat << ") -> (" << pixelPos.x() << "," << pixelPos.y() << ")";

    // 如果当前正在层级变化过程中，标记需要更新
    if (m_levelChanging)
    {
        m_pendingTargetUpdate = true;
    }
}

void Widget::updateAllTargetsForLevel()
{
    if (m_targets.isEmpty())
    {
        qDebug() << "没有目标需要更新位置";
        return;
    }

    int updatedCount = 0;
    for (auto it = m_targets.begin(); it != m_targets.end(); ++it)
    {
        TargetInfo& info = it.value();
        if (info.item)
        {
            // 重新计算像素位置
            QPoint oldPos = info.item->pos().toPoint();
            QPoint newPos = Bing::latLongToPixelXY(info.lon, info.lat, m_currentLevel);
            info.item->setPos(newPos);
            updatedCount++;

            qDebug() << "目标" << info.id << "位置更新: (" << oldPos.x() << "," << oldPos.y()
                     << ") -> (" << newPos.x() << "," << newPos.y() << ")";
        }
    }

    qDebug() << "已更新" << updatedCount << "个目标的位置，当前层级:" << m_currentLevel;
}

void Widget::sendTargetClicked(const QString& id)
{
    if (!m_udpSocket || !m_udpConnected)
    {
        qWarning() << "UDP未连接，无法发送消息";
        return;
    }

    // 构造JSON消息
    QJsonObject obj;
    obj["targetID"] = id;

    QJsonDocument doc(obj);
    QByteArray data = doc.toJson(QJsonDocument::Compact);

    // 发送到组播地址
    qint64 bytesWritten = m_udpSocket->writeDatagram(data, m_groupAddress, m_groupPort);

    if (bytesWritten == -1)
    {
        qWarning() << "发送UDP消息失败:" << m_udpSocket->errorString();
    }
    else
    {
        qDebug() << "发送目标点击消息:" << id;
    }
}

void Widget::hideTargetInfo()
{
    if (m_infoLabel)
    {
        m_infoLabel->hideInfo();
    }
    m_currentSelectedTarget.clear();
}

void Widget::checkTileLoadingComplete()
{
    if (m_levelChanging && m_pendingTargetUpdate)
    {
        qDebug() << "瓦片加载检查完成，更新目标位置";
        updateAllTargetsForLevel();
        m_levelChanging = false;
        m_pendingTargetUpdate = false;
    }
}
