#ifndef WIDGET_H
#define WIDGET_H

#include "geturl.h"
#include <QWidget>
#include <QUdpSocket>
#include <QHostAddress>
#include <QHash>
#include <QGraphicsPixmapItem>
#include <QLabel>
#include <QTimer>

QT_BEGIN_NAMESPACE

namespace Ui {
class Widget;
}

QT_END_NAMESPACE

// 目标信息结构体
struct TargetInfo {
    QString id;
    QString type;   // "red" or "blue"
    double lon = 0.0;
    double lat = 0.0;
    QGraphicsPixmapItem* item = nullptr;
};

// 自定义信息框标签
class InfoLabel : public QLabel
{
    Q_OBJECT
public:
    explicit InfoLabel(QWidget* parent = nullptr);
    void showTargetInfo(const TargetInfo& info, const QPoint& position);
    void hideInfo();

protected:
    void paintEvent(QPaintEvent* event) override;
};

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget* parent = nullptr);
    ~Widget();

protected:
    bool eventFilter(QObject* watched, QEvent* event) override;

private slots:
    void onUdpConfigClicked();
    void onReadyRead();
    void onMouseMove(QPoint scenePos);
    void onLevelChanged(int level);
    void onTileLoaded(const ImageInfo& info);

private:
    void showRect(QRect rect);
    void openUdpConfigDialog();
    bool connectUdp(const QString& group, quint16 port, const QString& ifaceName);
    void disconnectUdp();
    void processUdpMessage(const QByteArray& data);
    void upsertTarget(const QString& id, const QString& type, double lon, double lat);
    void updateAllTargetsForLevel();
    void sendTargetClicked(const QString& id);
    void hideTargetInfo();
    void checkTileLoadingComplete();

private:
    Ui::Widget* ui;
    GetUrl* m_geturl = nullptr;

    // UDP相关
    QUdpSocket* m_udpSocket = nullptr;
    QHostAddress m_groupAddress;
    quint16 m_groupPort = 0;
    QString m_interfaceName;
    bool m_udpConnected = false;

    // 地图状态
    int m_currentLevel = 5;
    bool m_levelChanging = false;
    bool m_pendingTargetUpdate = false;
    QTimer* m_tileCheckTimer = nullptr;

    // 目标管理
    QHash<QString, TargetInfo> m_targets;

    // 信息显示
    InfoLabel* m_infoLabel = nullptr;
    QString m_currentSelectedTarget;
};
#endif   // WIDGET_H
